#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将PDF报价单内容整理成表格
"""

import pandas as pd

def create_quotation_table():
    """创建报价单表格"""
    
    # 根据PDF内容整理的产品信息
    products_data = [
        # 产品一：接料宽皮带机
        {
            "序号": 1,
            "产品": "产品一",
            "品名": "接料宽皮带机（1米接料宽皮带机1000mm宽）",
            "数量": 2,
            "单价": 80000.00,
            "总价": 160000.00
        },
        
        # 产品二：多段伺服理料机相关设备
        {
            "序号": 2,
            "产品": "产品二",
            "品名": "多段伺服理料机加高速自动补面（型号：AF-7000-8/9）",
            "数量": 2,
            "单价": 80000.00,
            "总价": 160000.00
        },
        {
            "序号": 3,
            "产品": "产品二",
            "品名": "第二层补面装置1米挡料宽皮带机（1000mm宽）",
            "数量": 2,
            "单价": 30000.00,
            "总价": 60000.00
        },
        {
            "序号": 4,
            "产品": "产品二",
            "品名": "2米横接皮带机",
            "数量": 2,
            "单价": 20000.00,
            "总价": 40000.00
        },
        {
            "序号": 5,
            "产品": "产品二",
            "品名": "1.5米理料皮带机",
            "数量": 2,
            "单价": 40000.00,
            "总价": 80000.00
        },
        {
            "序号": 6,
            "产品": "产品二",
            "品名": "3米理料皮带机",
            "数量": 2,
            "单价": 60000.00,
            "总价": 120000.00
        },
        {
            "序号": 7,
            "产品": "产品二",
            "品名": "U型拐弯链板机（链接面机排面出口）物料排出、输送（共8-10米）",
            "数量": 2,
            "单价": 230000.00,
            "总价": 460000.00
        },
        
        # 产品三：全自动高速枕式包装机
        {
            "序号": 8,
            "产品": "产品三",
            "品名": "全自动高速枕式包装机（型号：S-5000AJ）",
            "数量": 2,
            "单价": 280000.00,
            "总价": 560000.00
        },
        {
            "序号": 9,
            "产品": "产品三",
            "品名": "8米推杆式传送带，前端过渡皮带",
            "数量": 2,
            "单价": 480000.00,
            "总价": 960000.00
        },
        {
            "序号": 10,
            "产品": "产品三",
            "品名": "热转印打印机：X65自动接膜装置，带电切刀",
            "数量": 2,
            "单价": 50000.00,
            "总价": 100000.00
        },
        {
            "序号": 11,
            "产品": "产品三",
            "品名": "X光检测",
            "数量": 2,
            "单价": 60000.00,
            "总价": 120000.00
        },
        {
            "序号": 12,
            "产品": "产品三",
            "品名": "万能制袋器排出传送带，带有气嘴剔除装置",
            "数量": 2,
            "单价": 150000.00,
            "总价": 300000.00
        },
        
        # 产品四：投包机
        {
            "序号": 13,
            "产品": "产品四",
            "品名": "投包机（型号：XDJ-2000C按三料包配置）",
            "数量": 2,
            "单价": 80000.00,
            "总价": 160000.00
        },
        {
            "序号": 14,
            "产品": "产品四",
            "品名": "重检机",
            "数量": 2,
            "单价": 40000.00,
            "总价": 80000.00
        },
        
        # 产品五：多轴伺服枕式五连包高速包装机
        {
            "序号": 15,
            "产品": "产品五",
            "品名": "多轴伺服枕式五连包高速包装机（下送膜形式）",
            "数量": 4,
            "单价": 90000.00,
            "总价": 360000.00
        },
        {
            "序号": 16,
            "产品": "产品五",
            "品名": "喷码机：马肯/多米诺",
            "数量": 12,
            "单价": 20000.00,
            "总价": 240000.00
        },
        {
            "序号": 17,
            "产品": "产品五",
            "品名": "全自动五连包自动上料ZD系列",
            "数量": 2,
            "单价": 450000.00,
            "总价": 900000.00
        },
        
        # 产品六：新型全自动袋面装箱机
        {
            "序号": 18,
            "产品": "产品六",
            "品名": "新型全自动袋面装箱机（型号：WDC-240-D-4）",
            "数量": 2,
            "单价": 50000.00,
            "总价": 100000.00
        },
        {
            "序号": 19,
            "产品": "产品六",
            "品名": "2米入料皮带机",
            "数量": 2,
            "单价": 10000.00,
            "总价": 20000.00
        },
        {
            "序号": 20,
            "产品": "产品六",
            "品名": "伺服推料装箱装置",
            "数量": 2,
            "单价": 400000.00,
            "总价": 800000.00
        },
        
        # 其他设备
        {
            "序号": 21,
            "产品": "产品七",
            "品名": "下纸箱机（型号：VCF-3000）",
            "数量": 1,
            "单价": 2000000.00,
            "总价": 2000000.00
        },
        {
            "序号": 22,
            "产品": "产品七",
            "品名": "下纸箱机（型号：VCF-1400）",
            "数量": 1,
            "单价": 150000.00,
            "总价": 150000.00
        },
        {
            "序号": 23,
            "产品": "其他",
            "品名": "包装及国内运输费",
            "数量": 1,
            "单价": 120000.00,
            "总价": 120000.00
        },
        {
            "序号": 24,
            "产品": "其他",
            "品名": "安装调试/售后服务费",
            "数量": 1,
            "单价": 220000.00,
            "总价": 220000.00
        },
        {
            "序号": 25,
            "产品": "其他",
            "品名": "食宿费（包括往返路上的时间）",
            "数量": 1,
            "单价": 100000.00,
            "总价": 100000.00
        }
    ]
    
    # 创建DataFrame
    df = pd.DataFrame(products_data)
    
    # 计算总计
    total_amount = df['总价'].sum()
    
    # 显示表格
    print("北京大森包装机械有限公司 - 袋面两条腿包装线报价单")
    print("=" * 80)
    print(f"客户：上海瑞勋国际贸易有限公司")
    print(f"联系人：马文峰")
    print(f"版本号：BOC-袋面高速S230-2025")
    print("=" * 80)
    print()
    
    # 设置pandas显示选项
    pd.set_option('display.max_columns', None)
    pd.set_option('display.width', None)
    pd.set_option('display.max_colwidth', 50)
    
    print(df.to_string(index=False))
    print()
    print("=" * 80)
    print(f"总价合计：¥{total_amount:,.2f}")
    print(f"优惠额度：¥83,700.00")
    print(f"优惠后总金额：¥8,286,300.00")
    print(f"未税金额：¥7,333,008.85")
    print("=" * 80)
    
    # 保存为Excel文件
    try:
        df.to_excel('北京大森报价单.xlsx', index=False, sheet_name='报价单')
        print(f"\n表格已保存为Excel文件：北京大森报价单.xlsx")
    except ImportError:
        print("\n注意：需要安装openpyxl库才能保存Excel文件")
        print("可以运行：pip install openpyxl")
    
    # 保存为CSV文件
    df.to_csv('北京大森报价单.csv', index=False, encoding='utf-8-sig')
    print(f"表格已保存为CSV文件：北京大森报价单.csv")
    
    return df

if __name__ == "__main__":
    df = create_quotation_table()
